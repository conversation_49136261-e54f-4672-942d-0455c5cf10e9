import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    fontFamily: "Nunito_400Regular",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: "#330065",
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginRight: 15,
  },
  searchInput: {
    flex: 1,
    color: "#FFFFFF",
    fontSize: 14,
    marginLeft: 10,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: "hidden",
  },
  profileImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
  },
  profileText: {
    fontSize: 20,
  },
  welcomeSection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: "#330065",
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 5,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: "#FFFFFF80",
  },
  addListContainer: {
    paddingHorizontal: 10,
    paddingTop: 20,
    paddingBottom: 30,
    backgroundColor: "#330065",
  },
  cardStack: {
    position: "relative",
    height: 180,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  sideCard: {
    transitionDuration: "0",
    position: "absolute",
    backgroundColor: "rgba(255, 255, 255, 1)",
    borderRadius: 20,
    width: 180,
    height: 160,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  leftCard: {
    transitionDuration: "0",
    position: "absolute",
    backgroundColor: "rgba(144, 238, 144, 1)",
    borderRadius: 20,
    width: 160,
    height: 180,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
    left: 30,
    top: -74,
  },
  rightCard: {
    transitionDuration: "0",
    position: "absolute",
    backgroundColor: "rgba(173, 216, 230, 1)",
    borderRadius: 20,
    width: 160,
    height: 180,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
    right: 30,
    top: -74,
  },
  mainCard: {
    transitionDuration: "0",
    position: "absolute",
    backgroundColor: "#F3ECFE",
    borderRadius: 20,
    width: 180,
    height: 180,
    top: -74,
    left: -96,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 8,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
  },
  addIconContainer: {
    width: 60,
    height: 60,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  addListTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 4,
    textAlign: "center",
  },
  addListSubtitle: {
    fontSize: 10,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 14,
    paddingHorizontal: 4,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1F2937",
  },
  categoriesScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  categoriesContainer: {
    paddingRight: 20,
  },
  categoryCard: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 15,
    width: 120,
    height: 100,
    marginRight: 15,
    overflow: "hidden",
  },
  categoryContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  categoryIconContainer: {
    marginBottom: 8,
  },
  categoryIcon: {
    fontSize: 24,
    color: "#FFFFFF",
  },
  categoryName: {
    fontSize: 14,
    color: "#FFFFFF",
    textAlign: "center",
    fontWeight: "500",
  },
  categoryBorder: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
  },
  eventsScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  eventCard: {
    flexDirection: "row",
    backgroundColor: "#F9FAFB",
    borderRadius: 12,
    padding: 15,
    marginRight: 15,
    width: 250,
  },
  eventDate: {
    width: 50,
    height: 50,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 15,
  },
  eventDateNumber: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  eventDateMonth: {
    fontSize: 8,
    color: "#FFFFFF",
    fontWeight: "600",
  },
  eventInfo: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  eventSubtitle: {
    fontSize: 12,
    color: "#6B7280",
  },
  picksScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  pickCard: {
    backgroundColor: "#F9FAFB",
    borderRadius: 12,
    padding: 15,
    marginRight: 15,
    width: 150,
  },
  pickImageContainer: {
    width: 120,
    height: 80,
    borderRadius: 8,
    marginBottom: 10,
    backgroundColor: "#E5E7EB",
    alignItems: "center",
    justifyContent: "center",
  },
  pickEmoji: {
    fontSize: 32,
  },
  pickName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 2,
  },
  pickSubtitle: {
    fontSize: 12,
    color: "#6B7280",
    marginBottom: 5,
  },
  pickPrice: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1F2937",
  },
  inspirationCard: {
    flexDirection: "row",
    backgroundColor: "#F9FAFB",
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  inspirationImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 15,
    backgroundColor: "#E5E7EB",
    alignItems: "center",
    justifyContent: "center",
  },
  inspirationEmoji: {
    fontSize: 24,
  },
  inspirationContent: {
    flex: 1,
  },
  inspirationTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  inspirationSubtitle: {
    fontSize: 12,
    color: "#6B7280",
    lineHeight: 16,
  },
  aiSection: {
    backgroundColor: "#6B46C1",
    margin: 20,
    borderRadius: 20,
    padding: 20,
    position: "relative",
    overflow: "hidden",
  },
  aiHeader: {
    alignItems: "flex-start",
    marginBottom: 10,
  },
  aiButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "bold",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  aiTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 10,
  },
  aiDescription: {
    fontSize: 14,
    color: "#FFFFFF",
    lineHeight: 20,
    marginBottom: 20,
    maxWidth: "80%",
  },
  aiChatButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 20,
  },
  aiRobotContainer: {
    position: "absolute",
    right: 20,
    bottom: 20,
    alignItems: "center",
  },
  aiRobot: {
    width: 80,
    height: 80,
    backgroundColor: "#FFFFFF",
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  robotEmoji: {
    fontSize: 40,
  },
  aiLogo: {
    backgroundColor: "#4C1D95",
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginTop: 10,
  },
  aiLogoText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
});
